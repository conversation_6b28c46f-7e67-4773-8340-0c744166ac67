# Scout Campaign Feature - Quick Start Guide

## Overview

The Scout Campaign feature allows you to run multiple KOL scouting workflows in sequence to achieve larger target goals while maintaining intelligent deduplication and progressive reporting. This is perfect for large-scale influencer discovery campaigns.

## Key Features

✅ **Campaign-Level Configuration**: Set total KOL targets across multiple workflow runs  
✅ **Intelligent Deduplication**: Automatically skip already scouted videos and creators  
✅ **Progressive Reporting**: Real-time statistics and batch reports  
✅ **Sequential Processing**: Challenge videos start from cursor 0, page by page (no random offsets)  
✅ **Rate Limit Respect**: Configurable concurrent task limits  
✅ **Persistent State**: Resume campaigns from where they left off  
✅ **Template System**: Pre-configured campaigns for common use cases  

## Quick Start

### 1. Run a Demo Campaign

```bash
# Run the full Japanese travel KOLs demo
npm run demo:campaign

# Run a small test campaign
npm run demo:campaign:test

# Show available templates
npm run demo:campaign:templates

# Demo pause/resume functionality
npm run demo:campaign:pause
```

### 2. Create Your Own Campaign

```typescript
import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';
import { createCampaignFromTemplate, getTemplate } from '@/campaigns/templates';

// Option 1: Use a template
const campaignConfig = createCampaignFromTemplate(
  getTemplate('gaming'), // or 'fashion', 'tech', 'food', 'fitness', etc.
  {
    campaignId: 'my-gaming-campaign-2024',
    targetKOLCount: 500,
    kolPerTask: 100,
    maxWorkflowRuns: 8,
  }
);

// Option 2: Create from scratch
const campaignConfig: CampaignConfig = {
  campaignId: 'my-custom-campaign',
  campaignName: 'My Custom KOL Campaign',
  description: 'Find specific type of influencers',
  
  targetKOLCount: 300,
  kolPerTask: 75,
  maxWorkflowRuns: 6,
  
  sharedConfig: {
    targetCreatorDescription: 'Your detailed creator requirements...',
    useIntelligentChallengeSelection: true,
    filterMode: 'LOOSE',
    minFollowers: 10000,
    // ... other parameters
  },
  
  concurrentTasksLimit: 4,
  persistenceType: 'json',
  outputDirectory: './campaign-results/my-custom-campaign',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

// Run the campaign
const campaign = new ScoutCampaign(campaignConfig, mastra);
const results = await campaign.runCampaign();
```

## Available Templates

| Template | Description | Target Followers |
|----------|-------------|------------------|
| `japaneseTravel` | Japanese travel-focused KOLs | 2k-20k |
| `gaming` | Gaming creators with diverse game coverage | 5k+ |
| `fashion` | Fashion & lifestyle influencers | 10k-100k |
| `tech` | Tech reviewers and innovation creators | 20k-200k |
| `food` | Food & cooking content creators | 5k-50k |
| `fitness` | Fitness & wellness influencers | 15k-100k |
| `test` | Small test campaign for development | 1k-10k |

## Campaign vs Single Workflow

### Single Workflow (Original)
- Finds 50-100 KOLs per run
- Random cursor offsets for variety
- No deduplication between runs
- Manual result aggregation

### Scout Campaign (New)
- Finds 300-500+ KOLs across multiple runs
- Sequential cursor processing (0, 25, 50, 75...)
- Automatic deduplication of videos and creators
- Progressive state management and reporting
- Automatic stopping when target is reached

## Configuration Options

### Essential Settings

```typescript
{
  // Campaign Identity
  campaignId: 'unique-campaign-id',
  campaignName: 'Human-readable name',
  description: 'Campaign description',
  
  // Targets
  targetKOLCount: 500,        // Total KOLs needed
  kolPerTask: 100,            // KOLs per workflow run
  maxWorkflowRuns: 10,        // Safety limit
  
  // Execution
  concurrentTasksLimit: 4,    // Rate limit (1-10)
  persistenceType: 'json',    // 'json' or 'sqlite'
  outputDirectory: './results',
  
  // Reporting
  enableProgressiveReporting: true,
  reportingInterval: 1,       // Report every N runs
}
```

### Workflow Configuration

All standard workflow parameters are supported in `sharedConfig`:

```typescript
sharedConfig: {
  targetCreatorDescription: 'Detailed requirements...',
  useIntelligentChallengeSelection: true,
  filterMode: 'LOOSE' | 'STRICT',
  minViews: 0,
  minLikes: 0,
  minComments: 0,
  minFollowers: 0,
  minRecentMedianViews: 0,
  minRecentMedianComments: 0,
  minRecentMedianLikes: 0,
}
```

## Output Structure

Each campaign creates a dedicated directory with:

```
./campaign-results/my-campaign-2024/
├── campaign-my-campaign-2024-state.json          # Campaign state
├── batch-1-2024-01-15T10-30-00-000Z.json        # Batch results
├── batch-2-2024-01-15T11-15-00-000Z.json        # Batch results
├── campaign-my-campaign-2024-batch-1-*.json     # Detailed results
├── campaign-my-campaign-2024-batch-1-*.csv      # CSV export
└── ...
```

## Campaign Controls

```typescript
// Check status
const status = campaign.getCampaignStatus();
console.log(`Progress: ${status.totalUniqueKOLs}/${config.targetKOLCount}`);

// Pause/Resume
campaign.pauseCampaign();
campaign.resumeCampaign();

// Get configuration
const config = campaign.getCampaignConfig();
```

## Best Practices

### 🎯 Planning
- Set realistic targets based on API rate limits
- Use 50-100 KOLs per task for optimal performance
- Plan for 5-10 workflow runs for large campaigns

### ⚡ Performance
- Start with `concurrentTasksLimit: 4`
- Monitor rate limit warnings in logs
- Use dedicated output directories per campaign

### 🔄 Execution
- Campaigns automatically resume from last state
- Monitor progress with progressive reporting
- Use pause/resume for long-running campaigns

### 📊 Results
- Each batch generates JSON and CSV files
- Final results include comprehensive statistics
- Use existing `saveCreatorResultsToFile` format

## Troubleshooting

### Common Issues

**Rate Limiting**: Reduce `concurrentTasksLimit` to 2-3  
**Memory Usage**: Use smaller `kolPerTask` values (25-50)  
**Disk Space**: Ensure sufficient space for result files  
**Network Errors**: Campaign will retry transient failures  

### Debug Mode

```bash
DEBUG=campaign:* npm run demo:campaign
```

### State Recovery

Campaigns automatically load existing state on creation. If a campaign fails, simply restart it with the same configuration - it will resume from where it left off.

## Integration with Existing Workflow

The Scout Campaign system is fully compatible with the existing workflow:

- ✅ Uses the same `creatorScoutWorkflow`
- ✅ Supports human interaction (suspended steps)
- ✅ Uses existing agents and services
- ✅ Generates same result formats
- ✅ Respects all existing configuration options

## Next Steps

1. **Try the demos**: Start with `npm run demo:campaign:test`
2. **Use templates**: Pick a template that matches your needs
3. **Customize**: Modify templates or create custom configurations
4. **Scale up**: Run larger campaigns with higher targets
5. **Monitor**: Use progressive reporting to track progress

## Support

For issues or questions:
- Check the logs for detailed error messages
- Review the campaign state file for debugging
- Use smaller test campaigns to validate configuration
- Refer to the comprehensive README in `/src/campaigns/`
