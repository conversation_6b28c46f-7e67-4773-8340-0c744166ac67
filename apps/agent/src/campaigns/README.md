# Scout Campaign System

The Scout Campaign system is an advanced feature that allows you to run multiple KOL scouting workflows in sequence to achieve larger target goals while maintaining deduplication and progressive reporting.

## Features

### 🎯 Campaign-Level Configuration
- **Target KOL Count**: Set total KOLs needed across all workflow runs
- **KOLs Per Task**: Configure how many KOLs each individual workflow should find
- **Max Workflow Runs**: Prevent infinite loops with maximum run limits
- **Shared Configuration**: Reuse the same workflow parameters across all runs

### 🔄 Intelligent Deduplication
- **Video ID Tracking**: Automatically skip already scouted videos
- **Creator ID Tracking**: Avoid duplicate creators across workflow runs
- **Progressive State**: Maintain state between workflow runs
- **Persistence**: Save campaign state to JSON files or SQLite database

### 📊 Progressive Reporting
- **Real-time Statistics**: Track progress, timing, and success rates
- **Batch Reports**: Get detailed reports after each workflow run
- **Final Summary**: Comprehensive campaign completion report
- **Configurable Intervals**: Control how often progress reports are generated

### 🚀 Enhanced Execution
- **Sequential Processing**: Challenge videos start from cursor 0, page by page
- **Rate Limit Respect**: Configurable concurrent task limits
- **Error Handling**: Robust retry logic and error recovery
- **Campaign Controls**: Pause, resume, and status monitoring

## Usage

### Basic Campaign Setup

```typescript
import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';
import { Mastra } from '@mastra/core';

// Configure your campaign
const campaignConfig: CampaignConfig = {
  campaignId: 'my-campaign-2024',
  campaignName: 'My KOL Campaign 2024',
  description: 'Find travel influencers for brand collaboration',
  
  // Campaign targets
  targetKOLCount: 500,        // Total KOLs needed
  kolPerTask: 100,            // KOLs per workflow run
  maxWorkflowRuns: 10,        // Maximum runs
  
  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: 'Travel influencers with 10k-100k followers',
    useIntelligentChallengeSelection: true,
    filterMode: 'LOOSE',
    minFollowers: 10000,
    // ... other workflow parameters
  },
  
  // Execution settings
  concurrentTasksLimit: 4,
  persistenceType: 'json',
  outputDirectory: './campaign-results/my-campaign-2024',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

// Create and run campaign
const campaign = new ScoutCampaign(campaignConfig, mastra);
const results = await campaign.runCampaign();
```

### Campaign Controls

```typescript
// Check campaign status
const status = campaign.getCampaignStatus();
console.log(`Progress: ${status.totalUniqueKOLs}/${campaignConfig.targetKOLCount}`);

// Pause campaign
campaign.pauseCampaign();

// Resume campaign
campaign.resumeCampaign();

// Get configuration
const config = campaign.getCampaignConfig();
```

## Configuration Options

### Campaign Configuration Schema

```typescript
interface CampaignConfig {
  // Identification
  campaignId: string;
  campaignName: string;
  description?: string;
  
  // Targets
  targetKOLCount: number;     // Total KOLs needed (default: 500)
  kolPerTask: number;         // KOLs per workflow run (default: 100)
  maxWorkflowRuns: number;    // Max runs (default: 10)
  
  // Shared workflow config
  sharedConfig: {
    targetCreatorDescription: string;
    useIntelligentChallengeSelection: boolean;
    filterMode: 'STRICT' | 'LOOSE';
    minViews: number;
    minLikes: number;
    minComments: number;
    minFollowers: number;
    minRecentMedianViews: number;
    minRecentMedianComments: number;
    minRecentMedianLikes: number;
  };
  
  // Execution settings
  concurrentTasksLimit: number;        // Rate limit (1-10, default: 4)
  persistenceType: 'json' | 'sqlite';  // Storage type (default: 'json')
  outputDirectory: string;             // Results directory
  enableProgressiveReporting: boolean; // Enable reports (default: true)
  reportingInterval: number;           // Report every N runs (default: 1)
}
```

### Campaign State Schema

```typescript
interface CampaignState {
  campaignId: string;
  status: 'running' | 'paused' | 'completed' | 'failed';
  startTime: string;
  lastUpdateTime: string;
  
  // Progress
  workflowRunsCompleted: number;
  totalUniqueKOLs: number;
  totalScoutedResults: number;
  
  // Deduplication
  scoutedVideoIds: string[];
  scoutedCreatorIds: string[];
  
  // Results
  allResults: any[];
  
  // Statistics
  statistics: {
    averageKOLsPerRun: number;
    totalExecutionTime: number;
    lastRunDuration: number;
    successfulRuns: number;
    failedRuns: number;
  };
}
```

## Running Demos

### Available Demo Scripts

```bash
# Run single workflow demo (original functionality)
npm run demo:workflow single

# Run campaign demo (new functionality)
npm run demo:workflow campaign

# Run dedicated campaign demos
npm run demo:campaign          # Full campaign demo
npm run demo:campaign:test     # Small test campaign
npm run demo:campaign:pause    # Pause/resume demo
```

### Demo Scenarios

1. **Full Campaign Demo**: Complete Japanese travel KOLs campaign
2. **Test Campaign Demo**: Small gaming creators campaign for testing
3. **Pause/Resume Demo**: Demonstrates campaign control functionality

## File Structure

```
apps/agent/src/campaigns/
├── ScoutCampaign.ts           # Main campaign class
├── README.md                  # This documentation
└── types.ts                   # Type definitions (if needed)

apps/agent/src/tests/
├── scout-campaign-demo.ts     # Dedicated campaign demos
└── human-interactive-scouter-workflow.ts  # Updated with campaign support
```

## Output Structure

### Campaign Results Directory

```
./campaign-results/my-campaign-2024/
├── campaign-my-campaign-2024-state.json     # Campaign state
├── batch-1-2024-01-15T10-30-00-000Z.json   # Batch 1 results
├── batch-2-2024-01-15T11-15-00-000Z.json   # Batch 2 results
├── campaign-my-campaign-2024-batch-1-2024-01-15T10-30-00-000Z.json  # Detailed results
├── campaign-my-campaign-2024-batch-1-2024-01-15T10-30-00-000Z.csv   # CSV export
└── ...
```

### Result Formats

Each batch generates:
- **Batch JSON**: Campaign-specific batch results with statistics
- **Detailed JSON**: Full creator data using existing `saveCreatorResultsToFile`
- **CSV Export**: Spreadsheet-ready format for analysis

## Key Differences from Single Workflow

### Campaign Mode Features

1. **Sequential Cursor Processing**: Always starts from cursor 0, processes page by page
2. **Deduplication**: Tracks and skips already scouted videos/creators
3. **Progressive State**: Maintains state between workflow runs
4. **Enhanced Logging**: Campaign-specific logging with batch context
5. **Automatic Stopping**: Stops when target is reached or max runs exceeded

### Workflow Integration

The campaign system integrates with existing workflows by:
- Passing campaign-specific flags in `inputData`
- Providing skip lists for already scouted content
- Maintaining human interaction capabilities
- Using existing result processing and saving functions

## Best Practices

### Campaign Planning

1. **Set Realistic Targets**: Consider API rate limits and processing time
2. **Configure Appropriate Batch Sizes**: Balance efficiency with rate limits
3. **Use Progressive Reporting**: Monitor progress and adjust if needed
4. **Plan for Interruptions**: Campaigns can be paused and resumed

### Performance Optimization

1. **Concurrent Task Limits**: Start with 4, adjust based on rate limit responses
2. **Batch Size**: 50-100 KOLs per task works well for most scenarios
3. **Output Directory**: Use dedicated directories for each campaign
4. **Persistence**: JSON is faster, SQLite is more robust for large campaigns

### Error Handling

1. **Monitor Logs**: Watch for rate limit warnings and API errors
2. **Use Retry Logic**: Built-in retry for transient failures
3. **Critical Error Detection**: Campaign stops for authentication/network issues
4. **State Recovery**: Campaign state is saved after each batch

## Troubleshooting

### Common Issues

1. **Rate Limiting**: Reduce `concurrentTasksLimit` if hitting rate limits
2. **Memory Usage**: Large campaigns may need memory optimization
3. **Disk Space**: Ensure sufficient space for result files
4. **Network Issues**: Campaign will retry transient network errors

### Debug Mode

Enable verbose logging by setting environment variables:
```bash
DEBUG=campaign:* npm run demo:campaign
```

### State Recovery

If a campaign fails, you can resume from the last saved state:
```typescript
// Campaign automatically loads existing state on creation
const campaign = new ScoutCampaign(campaignConfig, mastra);
// Will resume from where it left off
```

## Future Enhancements

Planned improvements:
- SQLite persistence implementation
- Web UI for campaign monitoring
- Advanced scheduling and automation
- Multi-campaign management
- Performance analytics and optimization
- Integration with external databases
