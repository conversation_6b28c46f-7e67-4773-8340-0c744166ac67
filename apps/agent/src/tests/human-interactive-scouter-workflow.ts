import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow';
import { Mastra, WatchEvent } from '@mastra/core';
import { input, select } from '@inquirer/prompts';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';
import { saveCreatorResultsToFile } from '@/utils/saveResults';
import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';

// Register the workflow
const mastra = new Mastra({
  agents: { creatorHashtagScout, creatorFilterAgent, challengePickerAgent },
  workflows: { creatorScoutWorkflow },
});

/**
 * Run a single workflow demo (original functionality)
 */
async function runSingleWorkflowDemo() {
  const registeredWorkflow = mastra.getWorkflow('creatorScoutWorkflow');
  const run = registeredWorkflow.createRun();

  // Start the workflow with content that needs review
  console.log('Starting kol scouter workflow...');

  run.watch(async (watchEvents: WatchEvent) => {
    // console.log('=== WATCH EVENT TRIGGERED ===');
    // console.log('Workflow state:', watchEvents.payload.workflowState);

    // TODO: THIS MIGHT BE A BUG FROM MASTRA.
    // Only process when workflow is in the 'running' state, not 'suspended'. But we do need that step to be suspended for the demo

    if (watchEvents.payload.workflowState.status !== 'running') {
      return;
    }
    const steps = Object.entries(watchEvents.payload.workflowState.steps);

    for (const [stepId, step] of steps) {
      const path = step;
      // console.log(`Step ${stepId} status:`, path?.status);

      if (path && path.status === 'suspended') {
        const { message, messages } = path.payload!;
        console.log('Messages count:', messages.length);
        console.log('\n===================================');
        console.log(message.text);
        console.log('===================================\n');

        const answer = await input({
          message: 'Please enter your response:',
        });

        console.log('user responded:', answer);

        await run.resume({
          step: stepId,
          resumeData: {
            messages,
            userInputMessage: answer,
          },
        });

        console.log('resumed');
      }
    }
  });

  const inputData = {
    targetCreatorDescription:
      // 'Help me find kols for Popmart \"The Monsters (like labubu)\" series unboxing, no numeric thresholds needed.',
      // 'Help me find Japanese KOLs: 1. Traveling focused content. 2. Follower from 2k to 20k. 3. Visited Chinese landmark before.',
      `Find me find some kols:
      - Ethnicity: Pure Japanese creators only (from their text/title/desc language and their appearance, not from metadata of region or language field)
      - Follower Count: Between 2,000 to 20,000 followers
      - Content Type:
        - Lifestyle
        - Everyday content
        - Vlog
        - Japan travel
      - Visual Constraints (thumbnail filtering only):
        - Must contains outdoor scenes on any thumbnail
        - Must contains face on any thumbnail`,
    // 'I need to find some American gaming creators with following rules: 1. They post videos about different games, not just one specific game. List out their recently posted games. 2. The median view count of their recent videos must greater than 50k. No other statistical requirements. 3. They speak English. 4. They show their faces and have voiceovers in their videos. 5. All posts are original content.',
    // 'I want to find kols who can promote the mobile game "Whiteout Survival" in Japan',
    // '一家智能投影仪品牌计划在中东市场进行内容投放，目标人群为35岁以下、有独立居住空间的科技早期用户，主要通过生活方式类博主触达。',
    useIntelligentChallengeSelection: true, // Test the intelligent selection
    desiredCreatorCount: 100,
    filterMode: 'LOOSE' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Campaign mode defaults (for single workflow runs)
    campaignMode: false,
    skipVideoIds: [],
    skipCreatorIds: [],
    sequentialCursorMode: false,
  };

  const result = await run.start({
    inputData,
  });

  // console.log('Final output:', JSON.stringify(result, null, 2));

  // Save comprehensive results to a single JSON file with all formats
  try {
    const resultsFilePath = await saveCreatorResultsToFile(result, inputData);

    console.log(`\n🎉 Workflow completed successfully!`);
    console.log(`📄 Comprehensive results saved to: ${resultsFilePath}`);
    console.log(`📋 File includes: Detailed, Summary, and CSV-ready formats`);
  } catch (error) {
    console.error('Failed to save results to file:', error);
  }

  // let isStepSuspended =
  //   result.activePaths.get('analyzeRequirement')?.status === 'suspended';

  // while (isStepSuspended) {
  //   const { message, messages } =
  //     result.activePaths.get('analyzeRequirement')?.suspendPayload;

  //   console.log('\n===================================');
  //   console.log(message);
  //   console.log('===================================\n');

  //   const answer = await input({
  //     message: 'Please enter your response:',
  //   });

  //   await run.resume({
  //     stepId: 'analyzeRequirement',
  //     context: {
  //       messages: messages,
  //       userInputMessage: answer,
  //     },
  //   });

  //   isStepSuspended =
  //     result.activePaths.get('analyzeRequirement')?.status === 'suspended';
  // }

  // console.log('Final output:', result.results);
}

/**
 * Run Scout Campaign demo (new functionality)
 */
async function runScoutCampaignDemo() {
  console.log('🎯 Starting Scout Campaign Demo...\n');

  const campaignConfig: CampaignConfig = {
    campaignId: 'japanese-kols-demo',
    campaignName: 'Japanese KOLs Demo Campaign',
    description: 'Demo campaign for Japanese KOLs',

    targetKOLCount: 200, // Total KOLs needed across all workflows
    kolPerTask: 20, // KOLs per individual workflow run
    maxWorkflowRuns: 2, // Maximum workflow runs

    sharedConfig: {
      targetCreatorDescription: `Find me some Japanese KOLs:
      - Ethnicity: Pure Japanese creators only (from their text/title/desc language and their appearance, not from metadata of region or language field)
      - Follower Count: Between 2,000 to 20,000 followers
      - Content Type:
        - Lifestyle
        - Everyday content
        - Vlog
        - Japan travel
      - Visual Constraints (thumbnail filtering only):
        - Must contains outdoor scenes on any thumbnail
        - Must contains face on any thumbnail`,
      useIntelligentChallengeSelection: true,
      filterMode: 'LOOSE' as const,
      minViews: 0,
      minLikes: 0,
      minComments: 0,
      minFollowers: 2000,
      minRecentMedianViews: 0,
      minRecentMedianComments: 0,
      minRecentMedianLikes: 0,
    },

    concurrentTasksLimit: 4,
    persistenceType: 'json',
    outputDirectory: './campaign-results/japanese-kols-ctrip',
    enableProgressiveReporting: true,
    reportingInterval: 1,
  };

  try {
    const campaign = new ScoutCampaign(campaignConfig, mastra);
    const results = await campaign.runCampaign();

    console.log('\n🎉 Scout Campaign completed successfully!');
    console.log(`📊 Total batches: ${results.length}`);
    console.log(
      `🎯 Total unique KOLs: ${campaign.getCampaignStatus().totalUniqueKOLs}`,
    );
  } catch (error) {
    console.error('❌ Scout Campaign failed:', error);
  }
}

/**
 * Main function to choose between single workflow or campaign
 */
async function main() {
  const mode = await select({
    message: 'Select demo mode:',
    choices: [
      { value: 'single', name: 'Single Workflow (original)' },
      { value: 'campaign', name: 'Scout Campaign (new)' },
    ],
  });

  console.log('🚀 KOL Scouter Demo System');
  console.log('==========================\n');

  switch (mode) {
    case 'single':
      console.log('Running single workflow demo...');
      await runSingleWorkflowDemo();
      break;

    case 'campaign':
      console.log('Running scout campaign demo...');
      await runScoutCampaignDemo();
      break;

    default:
      console.log('Available modes:');
      console.log('  single   - Run single workflow (original)');
      console.log('  campaign - Run scout campaign (new)');
      console.log('\nUsage: npm run demo [mode]');
      break;
  }
}

// Run the demo
main().catch(console.error);
